import requests
import time
from datetime import datetime

# Polygon.io API Configuration
POLYGON_API_KEY = "********************************"
POLYGON_REST_URL = "https://api.polygon.io"

class PolygonRESTClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]  # Add more symbols as needed
        self.running = False
        self.update_interval = 5  # seconds between updates
        
    def get_stock_quote(self, symbol):
        """Get real-time quote for a symbol"""
        try:
            url = f"{POLYGON_REST_URL}/v2/last/trade/{symbol}"
            params = {"apikey": self.api_key}
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"❌ Error fetching {symbol}: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching {symbol}: {e}")
            return None
    
    def get_stock_snapshot(self, symbol):
        """Get snapshot data for a symbol (includes more comprehensive data)"""
        try:
            # Try the previous day's close price endpoint (should work with free plan)
            url = f"{POLYGON_REST_URL}/v2/aggs/ticker/{symbol}/prev"
            params = {"apikey": self.api_key}
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"❌ Error fetching snapshot for {symbol}: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error fetching snapshot for {symbol}: {e}")
            return None
    
    def display_stock_data(self, symbol, data):
        """Display formatted stock data"""
        if not data or 'results' not in data:
            print(f"❌ No data available for {symbol}")
            return

        try:
            results = data['results']
            if len(results) > 0:
                stock_data = results[0]  # Get the first (and usually only) result
                
                open_price = stock_data.get('o', 'N/A')
                high_price = stock_data.get('h', 'N/A')
                low_price = stock_data.get('l', 'N/A')
                close_price = stock_data.get('c', 'N/A')
                volume = stock_data.get('v', 'N/A')
                timestamp = stock_data.get('t', 0)
                
                # Convert timestamp to readable format
                if timestamp:
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    date_str = dt.strftime('%Y-%m-%d')
                else:
                    date_str = 'N/A'
                
                print(f"📈 {symbol}: ${close_price} | Date: {date_str}")
                print(f"   📊 O:${open_price} H:${high_price} L:${low_price} C:${close_price} Vol:{volume}")
            else:
                print(f"❌ No results found for {symbol}")

        except Exception as e:
            print(f"❌ Error displaying data for {symbol}: {e}")
            print(f"Raw data: {data}")
    
    def fetch_all_symbols(self):
        """Fetch data for all symbols"""
        print(f"\n🔄 Fetching data at {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 60)
        
        for symbol in self.symbols:
            data = self.get_stock_snapshot(symbol)
            if data:
                self.display_stock_data(symbol, data)
            time.sleep(0.5)  # Small delay to avoid rate limiting
    
    def start_polling(self):
        """Start polling for stock data"""
        self.running = True
        print("📶 Starting Polygon REST API polling...")
        print(f"🔄 Updating every {self.update_interval} seconds")
        print(f"📊 Tracking symbols: {', '.join(self.symbols)}")
        
        while self.running:
            try:
                self.fetch_all_symbols()
                time.sleep(self.update_interval)
            except KeyboardInterrupt:
                print("\n🛑 Stopping...")
                self.running = False
                break
            except Exception as e:
                print(f"❌ Error in polling loop: {e}")
                time.sleep(self.update_interval)
    
    def stop(self):
        """Stop the polling"""
        self.running = False

if __name__ == "__main__":
    client = PolygonRESTClient(POLYGON_API_KEY)
    
    try:
        client.start_polling()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        client.stop()
